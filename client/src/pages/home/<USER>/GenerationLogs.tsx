import { type FC, memo } from 'react';

import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { useObservableState } from '@/shared/hooks/useObservableState';

import { type HomeController } from '../HomeController';

interface GenerationLogsProps {
  controller: HomeController;
}

export const GenerationLogs: FC<GenerationLogsProps> = memo(({ controller }) => {
  const logs = useObservableState(controller.generationLogs$, []);

  if (logs.length === 0) {
    return (
      <Card className="w-full h-full">
        <CardHeader>
          <CardTitle>生成日志</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <p className="text-gray-500">等待生成开始...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full h-full">
      <CardHeader>
        <CardTitle>生成日志</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-full max-h-[calc(100vh-200px)] overflow-y-auto space-y-2">
          {logs.map((log, index) => (
            <div
              key={index}
              className="text-sm text-gray-700 font-mono bg-gray-50 px-3 py-2 rounded border-l-4 border-blue-200"
            >
              <span className="text-gray-500 text-xs mr-2">
                [{String(index + 1).padStart(2, '0')}]
              </span>
              {log}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
});

GenerationLogs.displayName = 'GenerationLogs';
