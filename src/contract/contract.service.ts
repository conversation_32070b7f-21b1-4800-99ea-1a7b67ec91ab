import { Injectable, Logger, MessageEvent } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { Observable } from 'rxjs';

import { ResultResponse, ResultUtil } from '../common/utils/result.util';
import { LlmManagerService } from '../llm/llm-manager.service';
import { PromptTemplateService } from '../prompt-template/prompt-template.service';

import { ContractTemplatesService } from './contract-templates.service';
import { ContractTemplate } from './interfaces';

@Injectable()
export class ContractService {
  private readonly logger = new Logger(ContractService.name);
  private readonly model: string;

  constructor(
    private readonly promptTemplateService: PromptTemplateService,
    private readonly llmManagerService: LlmManagerService,
    private readonly contractTemplatesService: ContractTemplatesService,
    private readonly configService: ConfigService,
  ) {
    // 默认 DeepSeek-32B
    this.model = this.configService.get<string>('LLM_MODEL', 'llm-reasoning-default');
  }

  public generateContractSse(userInput: string): Observable<MessageEvent> {
    return new Observable<MessageEvent>((observer) => {
      const logPush = (msg: string) => {
        observer.next({ data: JSON.stringify({ log: msg }) });
      };
      this.generateContractWithLogCallback(userInput, logPush)
        .then((result) => {
          observer.next({ data: JSON.stringify(result) });
        })
        .finally(() => {
          observer.next({ data: '[END]' });
          observer.complete();
        });
    });
  }

  // 生成合同流程
  private async generateContractWithLogCallback(
    userInput: string,
    onLog: (msg: string) => void,
  ): Promise<ResultResponse<any>> {
    return ResultUtil.execute(async () => {
      // 0. 加载所有模板摘要（内存获取）
      const templates = this.contractTemplatesService.getAllTemplates();
      if (!templates || !Array.isArray(templates) || templates.length === 0) {
        onLog('步骤0: 模板加载失败');
        return ResultUtil.fail('步骤0: 模板加载失败', 'TEMPLATE_LOAD_ERROR');
      }

      onLog('步骤1: 正在选择合适的合同模板...');
      // 1. 智能选择模板（LLM调用）
      const promptSelect = this.promptTemplateService.getPrompt('template_selection');
      if (!promptSelect) {
        onLog('步骤1-1: Prompt(template_selection)未配置');
        return ResultUtil.fail('步骤1-1: Prompt(template_selection)未配置', 'PROMPT_NOT_FOUND');
      }

      // 抽离总结信息
      const templatesSummary = templates.map((t) => ({
        template_filename: t.template_filename,
        contract_type: t.contract_type ?? null,
        template_note: t.template_note ?? null,
        contract_title: t.metadata?.contract_title ?? null,
      }));
      const prompt = promptSelect.user_prompt_template
        .replace('{{user_input}}', userInput)
        .replace('{{templates_summary}}', JSON.stringify(templatesSummary));
      const selectResult = await this.llmManagerService.callLLMUseOpenAISDK(
        prompt,
        {
          model: this.model,
          systemPrompt: promptSelect.system_message,
        },
        { headers: { 'x-model': this.model } },
      );
      const selectContent = selectResult.data as string;
      if (!selectContent) {
        onLog('步骤1-2: 模板选择失败, LLM无响应');
        return ResultUtil.fail('步骤1-2: 模板选择失败, LLM无响应', 'TEMPLATE_SELECT_ERROR');
      }

      const selectedTemplateFilename = this.extractTemplateFilename(selectContent);
      if (!selectedTemplateFilename) {
        onLog('步骤1-3: LLM未返回有效的模板文件名');
        return ResultUtil.fail('步骤1-2: LLM未返回有效的模板文件名', 'TEMPLATE_SELECT_ERROR');
      }

      const templateData: ContractTemplate | undefined =
        this.contractTemplatesService.getTemplateByFilename(selectedTemplateFilename);
      if (!templateData) {
        onLog('步骤1-4: LLM返回的模板名文件不存在');
        return ResultUtil.fail('步骤1-4: LLM返回的模板名文件不存在', 'TEMPLATE_NOT_FOUND');
      }

      // 2. 分组收集需要LLM处理的文本片段
      onLog('步骤2: 正在从模板中收集各逻辑块的文本内容...');
      // 获取提示词
      const promptBlock = this.promptTemplateService.getPrompt('content_processing');
      if (!promptBlock) {
        onLog(`步骤2-0: 未配置content_processing prompt,将使用原始模板.`);
      }

      const copyData = JSON.parse(JSON.stringify(templateData));
      const groupedTextFragments = this.collectTextFragmentsByGroup(copyData);

      if (Object.keys(groupedTextFragments).length === 0) {
        onLog('步骤2-1: 未在模板中找到任何可供LLM处理的文本片段组,将使用原始模板.');
      } else {
        onLog(`步骤2-1: 成功收集到 ${Object.keys(groupedTextFragments).length} 个文本逻辑块。`);
      }

      // 2-2. 按顺序调用LLM处理文本片段
      const orderKeys = promptBlock
        ? ['metadata_block', 'main_content_block', 'payment_block', 'standard_clauses_block']
        : [];

      for (const groupId of orderKeys) {
        const block = groupedTextFragments[groupId];

        if (!block || Object.keys(block).length === 0) {
          onLog(`步骤2-2: 跳过空的或不存在的逻辑块: ${groupId}`);
          continue;
        }

        onLog(`步骤2-2: 正在处理逻辑块 '${groupId}' ...`);
        const prompt = promptBlock.user_prompt_template
          .replace('{{user_input}}', userInput)
          .replace('{{contract_group_block}}', JSON.stringify(block));
        const llmBlockResult = await this.llmManagerService.callLLMUseOpenAISDK(
          prompt,
          {
            model: this.model,
            systemPrompt: promptBlock.system_message,
            parseJson: true,
          },
          { headers: { 'x-model': this.model } },
        );

        if (!llmBlockResult.success) {
          onLog(`步骤2-2: LLM返回内容失败, 将跳过逻辑块${groupId}`);
          continue;
        }

        this.applyProcessedBlock(copyData, block, llmBlockResult.data!);
        onLog(`步骤2-2: 逻辑块 '${groupId}' 处理完成。`);
      }

      // 3. 渲染为Markdown
      onLog('步骤3: 正在生成合同内容...');
      const markdown = this.buildMarkdownFromJson(copyData);
      onLog('步骤3: 合同内容生成成功。');

      return ResultUtil.success(
        {
          contract_markdown: markdown,
          selected_template_name: selectedTemplateFilename,
        },
        '合同已生成',
      );
    }, '合同生成失败');
  }

  private extractTemplateFilename(content: string): string | null {
    const filenameMatch = content.match(/template_filename:\s*([^\s\n]+)/);
    if (filenameMatch) return filenameMatch[1].trim();
    const jsonMatch = content.match(/([a-zA-Z0-9\u4e00-\u9fa5\-_【】（）()]+\.json)/);
    if (jsonMatch) return jsonMatch[1].trim();
    if (content.endsWith('.json')) return content.trim();
    return null;
  }

  /**
   * 判断某个字段是否需要 LLM 处理（严格对标 app.py _is_value_needing_processing）
   */
  private isValueNeedingProcessing(keyPath: (string | number)[], value: any): boolean {
    if (typeof value !== 'string' || !value.trim()) return false;
    // 顶层键直接判断
    if (keyPath.length === 1) {
      const key = keyPath[0];
      if (['template_version', 'template_note', 'format'].includes(String(key))) return false;
      if (value.includes('【') || value.includes('[')) return true;
      return ['contract_type', 'preamble'].includes(String(key));
    }
    // 多层路径判断
    const lastKey = String(keyPath[keyPath.length - 1]);
    if (['section_number', 'subsection_number', 'purpose', 'format'].includes(lastKey))
      return false;
    if (keyPath.length > 1 && keyPath[keyPath.length - 2] === 'key_points') return false;
    if (keyPath[0] === 'formatting_notes') return false;
    if (keyPath[0] === 'metadata') {
      return ['contract_number', 'contract_title', 'sign_date'].includes(lastKey);
    }
    if (keyPath[0] === 'parties') {
      return ['name', 'role', 'address', 'contact_person', 'contact_method'].includes(lastKey);
    }
    if (keyPath[0] === 'data_description') {
      return ['data_name', 'data_content', 'data_nature', 'exclusions'].includes(lastKey);
    }
    // sections/subsections/list_content/table_content 内部的字符串
    let isContentLike = false;
    for (const pKey of keyPath) {
      if (
        [
          'sections',
          'subsections',
          'list_content',
          'table_content',
          'content',
          'section_title',
          'subsection_title',
        ].includes(String(pKey))
      ) {
        isContentLike = true;
        break;
      }
    }
    if (isContentLike) {
      return !['section_number', 'subsection_number', 'purpose', 'format'].includes(lastKey);
    }
    if (keyPath[0] === 'signature_block') {
      return ['company_seal', 'date', 'representative'].includes(lastKey);
    }
    return false;
  }

  /**
   * 递归收集所有带llm_group_id的文本片段，按group分组（严格调用isValueNeedingProcessing）
   */
  private collectTextFragmentsByGroup(templateData: any): Record<string, Record<string, string>> {
    const groupsData: Record<string, Record<string, string>> = {};
    const traverse = (
      node: any,
      currentPath: (string | number)[],
      currentGroupId: string | null,
    ) => {
      if (typeof node === 'object' && node !== null) {
        const nodeGroupId =
          typeof node.llm_group_id === 'string' ? node.llm_group_id : currentGroupId;
        for (const key in node) {
          if (key === 'llm_group_id') continue;
          traverse(node[key], [...currentPath, key], nodeGroupId);
        }
      } else if (Array.isArray(node)) {
        node!.forEach((item, idx) => {
          traverse(item, [...currentPath, idx], currentGroupId);
        });
      } else if (typeof node === 'string' && node.trim() && currentGroupId) {
        if (this.isValueNeedingProcessing(currentPath, node)) {
          const pathKey = currentPath.join('.');
          if (!groupsData[currentGroupId]) groupsData[currentGroupId] = {};
          groupsData[currentGroupId][pathKey] = node;
        }
      }
    };
    traverse(templateData, [], null);
    return groupsData;
  }

  /**
   * 递归回填LLM处理结果到模板
   */
  private applyProcessedBlock(
    templateData: any,
    block: Record<string, string>,
    processedBlock: any,
  ) {
    for (const pathKey in processedBlock) {
      const value = processedBlock[pathKey];
      if (typeof value !== 'string' || value.trim().toLowerCase() === '[nochange]') continue;
      // 路径如 sections.0.content
      const pathArr = pathKey.split('.').map((k) => (isNaN(Number(k)) ? k : Number(k)));
      let current = templateData;
      for (let i = 0; i < pathArr.length - 1; i++) {
        current = current[pathArr[i]];
      }
      const lastKey = pathArr[pathArr.length - 1];
      current[lastKey] = value;
    }
  }

  /**
   * 严格按照app.py _build_markdown_from_json递归实现Markdown渲染
   */
  private buildMarkdownFromJson(data: any): string {
    let md = '';
    // 1. 合同标题
    if (data.metadata?.contract_title) {
      md += `# ${data.metadata.contract_title}\n\n`;
    }
    // 2. 合同编号
    if (data.metadata?.contract_number) {
      md += `**${data.metadata.contract_number}**\n\n`;
    }
    // 3. 签订日期
    if (data.metadata?.sign_date) {
      md += `**签订时间：${data.metadata.sign_date}**\n\n`;
    }
    // 4. 甲乙双方信息
    const parties = data.parties || {};
    const partyA = parties.party_a || {};
    const partyB = parties.party_b || {};
    if (partyA.name) {
      md += `**甲方：${partyA.name}**\n`;
      if (partyA.role) md += `（${partyA.role}）\n`;
      md += '\n';
    }
    if (partyA.address) md += `联系地址：${partyA.address}\n\n`;
    if (partyA.contact_person) md += `联系人：${partyA.contact_person}\n\n`;
    if (partyA.contact_method) md += `联系方式：${partyA.contact_method}\n\n`;
    if (partyB.name) {
      md += `**乙方：${partyB.name}**\n`;
      if (partyB.role) md += `（${partyB.role}）\n`;
      md += '\n';
    }
    if (partyB.address) md += `联系地址：${partyB.address}\n\n`;
    if (partyB.contact_person) md += `联系人：${partyB.contact_person}\n\n`;
    if (partyB.contact_method) md += `联系方式：${partyB.contact_method}\n\n`;
    if (partyA.name || partyB.name) {
      md += '甲方和乙方在本合同中单独称为"一方"，合称"双方"。\n\n';
    }
    // 5. 序言
    if (data.preamble && typeof data.preamble === 'string') {
      md += `## 序言\n\n${data.preamble}\n\n`;
    }
    // 6. 数据描述
    const dataDesc = data.data_description || {};
    if (Object.keys(dataDesc).length > 0) {
      md += '## 数据描述\n\n';
      if (dataDesc.data_name) md += `**数据名称：** ${dataDesc.data_name}\n\n`;
      if (dataDesc.data_content) md += `**数据内容：** ${dataDesc.data_content}\n\n`;
      if (dataDesc.data_nature) md += `**数据性质：** ${dataDesc.data_nature}\n\n`;
      if (dataDesc.exclusions) md += `**排除条款：** ${dataDesc.exclusions}\n\n`;
    }
    // 7. 章节
    if (Array.isArray(data.sections)) {
      data.sections.forEach((section: any) => {
        if (typeof section !== 'object') return;
        const secNum = section.section_number || '';
        const secTitle = section.section_title || '';
        if (secNum && secTitle) {
          md += `## ${secNum}、${secTitle}\n\n`;
        } else if (secTitle) {
          md += `## ${secTitle}\n\n`;
        }
        if (section.content) md += `${section.content}\n\n`;
        // 子章节
        if (Array.isArray(section.subsections)) {
          section.subsections.forEach((sub: any) => {
            if (typeof sub !== 'object') return;
            const subNum = sub.subsection_number || '';
            const subTitle = sub.subsection_title || '';
            if (subTitle) {
              md += `### ${subNum}、${subTitle}\n\n`;
            } else if (subNum) {
              md += `**${subNum}.** `;
            }
            if (sub.content) md += `${sub.content}\n\n`;
          });
        }
      });
    }
    // 8. 签署区
    const sigBlock = data.signature_block;
    if (sigBlock) {
      md += '## 签署区\n\n';
      if (typeof sigBlock === 'object') {
        const partyASig = sigBlock.party_a_signature || {};
        const partyBSig = sigBlock.party_b_signature || {};
        if (partyASig.company_seal) md += `**${partyASig.company_seal}**\n\n`;
        if (partyASig.date) md += `${partyASig.date}\n\n`;
        if (partyBSig.company_seal) md += `**${partyBSig.company_seal}**\n\n`;
        if (partyBSig.date) md += `${partyBSig.date}\n\n`;
      } else if (typeof sigBlock === 'string') {
        md += `${sigBlock}\n\n`;
      }
    }
    return md;
  }
}
